<?php
/**
 * SSRF (Server-Side Request Forgery) 漏洞测试文件
 * 
 * 警告：此文件仅用于安全测试和教育目的
 * 请勿在生产环境中使用
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSRF 漏洞测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            resize: vertical;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .examples {
            margin-top: 20px;
        }
        .example-link {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
            font-size: 12px;
        }
        .example-link:hover {
            background-color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SSRF 漏洞测试工具</h1>
        
        <div class="warning">
            <strong>⚠️ 警告：</strong> 此工具仅用于安全测试和教育目的。请勿在生产环境中使用，也不要用于恶意攻击。
        </div>

        <form method="POST" action="">
            <div class="form-group">
                <label for="url">目标URL：</label>
                <input type="text"
                       id="url"
                       name="url"
                       placeholder="请输入要请求的URL，例如：http://httpbin.org/ip"
                       value="<?php echo isset($_POST['url']) ? htmlspecialchars($_POST['url']) : ''; ?>">
            </div>
            <div class="form-group">
                <label for="request_method">请求方法：</label>
                <select id="request_method" name="request_method">
                    <option value="GET" <?php echo (isset($_POST['request_method']) && $_POST['request_method'] == 'GET') ? 'selected' : ''; ?>>GET</option>
                    <option value="POST" <?php echo (isset($_POST['request_method']) && $_POST['request_method'] == 'POST') ? 'selected' : ''; ?>>POST</option>
                </select>
            </div>
            <div class="form-group">
                <label for="post_data">POST数据 (仅当请求方法为POST时使用)：</label>
                <textarea id="post_data"
                         name="post_data"
                         rows="3"
                         placeholder="例如：key=admin123&param2=value2"><?php echo isset($_POST['post_data']) ? htmlspecialchars($_POST['post_data']) : ''; ?></textarea>
            </div>
            <button type="submit">发送请求</button>
        </form>

        <div class="examples">
            <h3>测试示例：</h3>
            <button type="button" onclick="setExample('http://httpbin.org/ip', 'GET', '')" class="example-link">外部IP检测</button>
            <button type="button" onclick="setExample('http://httpbin.org/headers', 'GET', '')" class="example-link">请求头信息</button>
            <button type="button" onclick="setExample('http://127.0.0.1:80', 'GET', '')" class="example-link">本地端口扫描</button>
            <button type="button" onclick="setExample('http://localhost/flag.php', 'POST', 'key=admin123')" class="example-link">Flag测试(POST)</button>
            <button type="button" onclick="setExample('http://127.0.0.1/flag.php', 'POST', 'key=admin123')" class="example-link">内网Flag测试</button>
            <button type="button" onclick="setExample('http://***************/latest/meta-data/', 'GET', '')" class="example-link">AWS元数据</button>
            <button type="button" onclick="setExample('file:///etc/passwd', 'GET', '')" class="example-link">本地文件读取</button>
        </div>

        <script>
        function setExample(url, method, postData) {
            document.getElementById('url').value = url;
            document.getElementById('request_method').value = method;
            document.getElementById('post_data').value = postData;
        }
        </script>

        <?php
        if (isset($_POST['url']) && !empty($_POST['url'])) {
            $url = $_POST['url'];
            $request_method = isset($_POST['request_method']) ? $_POST['request_method'] : 'GET';
            $post_data = isset($_POST['post_data']) ? $_POST['post_data'] : '';

            echo $url;
            
            echo '<div class="result">';
            echo '<h3>请求结果：</h3>';
            echo '<p><strong>目标URL：</strong> ' . htmlspecialchars($url) . '</p>';
            echo '<p><strong>请求方法：</strong> ' . htmlspecialchars($request_method) . '</p>';
            if ($request_method == 'POST' && !empty($post_data)) {
                echo '<p><strong>POST数据：</strong> ' . htmlspecialchars($post_data) . '</p>';
            }
            
            // 记录请求时间
            $start_time = microtime(true);
            
            try {
                $response = '';
                $http_code = 0;
                $content_type = '';
                $error = '';

                // 检查是否有cURL扩展
                if (function_exists('curl_init')) {
                    // 使用 cURL 发送请求（存在SSRF漏洞的代码）
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    curl_setopt($ch, CURLOPT_USERAGENT, 'SSRF-Test-Tool/1.0');
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                    // 如果是POST请求，设置POST数据
                    if ($request_method == 'POST') {
                        curl_setopt($ch, CURLOPT_POST, true);
                        if (!empty($post_data)) {
                            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                                'Content-Type: application/x-www-form-urlencoded'
                            ));
                        }
                    }

                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
                    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
                    $error = curl_error($ch);

                    curl_close($ch);
                    $method_used = 'cURL';
                } else {
                    // 使用 file_get_contents 作为备选方案（同样存在SSRF漏洞）
                    $context_options = array(
                        'http' => array(
                            'method' => $request_method,
                            'timeout' => 10,
                            'user_agent' => 'SSRF-Test-Tool/1.0',
                            'follow_location' => 1,
                            'max_redirects' => 5
                        ),
                        'ssl' => array(
                            'verify_peer' => false,
                            'verify_peer_name' => false
                        )
                    );

                    // 如果是POST请求，添加POST数据
                    if ($request_method == 'POST' && !empty($post_data)) {
                        $context_options['http']['header'] = "Content-Type: application/x-www-form-urlencoded\r\n";
                        $context_options['http']['content'] = $post_data;
                    }

                    $context = stream_context_create($context_options);
                    $response = @file_get_contents($url, false, $context);

                    if ($response === false) {
                        $error = error_get_last()['message'] ?? '请求失败';
                    } else {
                        // 尝试从响应头获取状态码
                        if (isset($http_response_header)) {
                            $status_line = $http_response_header[0] ?? '';
                            if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $status_line, $matches)) {
                                $http_code = intval($matches[1]);
                            }

                            // 查找Content-Type头
                            foreach ($http_response_header as $header) {
                                if (stripos($header, 'content-type:') === 0) {
                                    $content_type = trim(substr($header, 13));
                                    break;
                                }
                            }
                        }
                        if ($http_code == 0) $http_code = 200; // 默认假设成功
                    }
                    $method_used = 'file_get_contents';
                }

                $end_time = microtime(true);
                $execution_time = round(($end_time - $start_time) * 1000, 2);

                if ($error) {
                    echo '<div class="error">';
                    echo '<p><strong>❌ 请求失败：</strong></p>';
                    echo '<p>' . htmlspecialchars($error) . '</p>';
                    echo '<p><strong>使用方法：</strong> ' . $method_used . '</p>';
                    echo '</div>';
                } else {
                    echo '<div class="success">';
                    echo '<p><strong>✅ 请求成功</strong></p>';
                    echo '<p><strong>使用方法：</strong> ' . $method_used . '</p>';
                    echo '<p><strong>HTTP状态码：</strong> ' . ($http_code ?: '未知') . '</p>';
                    echo '<p><strong>内容类型：</strong> ' . htmlspecialchars($content_type ?: '未知') . '</p>';
                    echo '<p><strong>响应时间：</strong> ' . $execution_time . ' ms</p>';
                    echo '<p><strong>响应内容：</strong></p>';

                    // 限制响应内容长度以避免页面过长
                    $display_content = strlen($response) > 2000 ?
                        substr($response, 0, 2000) . "\n\n... (内容已截断，总长度: " . strlen($response) . " 字符)" :
                        $response;

                    echo '<pre>' . htmlspecialchars($display_content) . '</pre>';
                    echo '</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="error">';
                echo '<p><strong>❌ 发生异常：</strong></p>';
                echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '</div>';
            }
            
            echo '</div>';
        }
        ?>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
            <h3>🛡️ SSRF 防护建议：</h3>
            <ul>
                <li>对用户输入的URL进行严格验证和过滤</li>
                <li>使用白名单限制允许访问的域名和IP</li>
                <li>禁止访问内网地址（127.0.0.1, 10.x.x.x, 192.168.x.x, 169.254.x.x等）</li>
                <li>禁止访问敏感端口和协议（file://, gopher://, dict://等）</li>
                <li>设置合理的超时时间和重定向限制</li>
                <li>在网络层面进行访问控制</li>
            </ul>
        </div>
    </div>
</body>
</html>
