<?php
/**
 * Flag服务端文件
 * 用于SSRF测试，需要正确的key才能获取flag
 */

// 设置响应头
header('Content-Type: text/plain; charset=UTF-8');

// 正确的key值（在实际环境中这应该是更复杂的值）
$correct_key = "admin123";

// 获取POST请求参数 (仅支持 application/x-www-form-urlencoded)
$key = isset($_POST['key']) ? $_POST['key'] : '';
$all_post_data = $_POST;

if (empty($key)) {
    // 没有提供key
    http_response_code(401);
    echo "ERROR: Access denied\n";
    echo "Key parameter is required\n";
    echo "Content-Type received: " . $content_type . "\n";
    echo "POST data received: " . http_build_query($all_post_data) . "\n";
    echo "Expected format: application/x-www-form-urlencoded\n";
    echo "Example: key=admin123\n";
} elseif ($key !== $correct_key) {
    // key不正确
    http_response_code(403);
    echo "ERROR: Access denied\n";
    echo "Invalid key provided\n";
    echo "Provided key: " . $key . "\n";
    echo "Content-Type received: " . $content_type . "\n";
    echo "POST data received: " . http_build_query($all_post_data) . "\n";
    echo "Expected key: admin123\n";
} else {
    // key正确，返回flag
    http_response_code(200);
    echo "SUCCESS: Access granted!\n";
    echo "flag{you_got_me}\n";
    echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
    echo "Congratulations! You successfully exploited the SSRF vulnerability!\n";
    echo "Content-Type received: " . $content_type . "\n";
    echo "POST data received: " . http_build_query($all_post_data) . "\n";
    echo "Provided key: " . $key . "\n";
}

// 记录访问日志（可选）
$status = empty($key) ? 'no_key' : ($key !== $correct_key ? 'invalid_key' : 'success');
$log_entry = date('Y-m-d H:i:s') . " - IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') .
             " - Key: " . ($key ?: 'none') . " - Status: " . $status . "\n";
file_put_contents('flag_access.log', $log_entry, FILE_APPEND | LOCK_EX);
?>
